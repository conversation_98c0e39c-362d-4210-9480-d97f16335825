# Stripe Tax Integration and VAT Object Implementation

## Overview

This implementation integrates Stripe Tax for automatic VAT calculation and replaces the hardcoded VAT percentage with a dynamic VAT object structure that stores both the percentage and exemption reasons.

## Changes Made

### 1. Database Schema Changes

**File:** `supabase/migrations/20250527195119_add_vat_to_proposals.sql`

- Added `vat` JSONB column to the `proposals` table
- Structure: `{percent: string, reason_without: string | null}`
- Added GIN index for efficient querying

### 2. TypeScript Models and Validators

**Files:**
- `client/src/lib/models/proposal.model.ts`
- `client/src/lib/validators/proposal.validator.ts`

- Added `VatInfo` interface with `percent: number` and `reason_without: string | null` fields
- Updated `Proposal` interface to include optional `vat` field
- Added Zod schema validation for VAT information

### 3. VAT Calculator Service

**File:** `client/src/api/services/vat-calculator.service.ts`

New service that:
- Extracts VAT information directly from Stripe Tax breakdown
- Uses Stripe's `total_details.breakdown.tax` array to find VAT entries
- Maps Stripe's `taxability_reason` to Bulgarian VAT exemption reasons:
  - `reverse_charge` → `"чл.86, ал.3 и чл.21 от ЗДДС"` (Bulgarian companies with VAT)
  - `zero_rated` → `"чл.21 ал.2 от ЗДДС"` (EU companies with VAT number)
  - `customer_exempt`/`not_subject_to_tax` → `"чл.21 от ЗДДС"` (International clients)
  - `not_collecting`/`not_supported` → `"чл.82, ал. 2 от ЗДДС"` (Domestic without VAT)
  - `product_exempt` → `"чл.113 ал.9 от ЗДДС"` (General exemption/default)

### 4. Stripe Tax Integration

**File:** `client/src/api/routes/proposals.routes.ts`

#### Payment Creation Endpoint (`POST /:id/payments`)
- Enabled Stripe Tax with `automatic_tax: { enabled: true }`
- Enhanced proposal query to include client billing information
- Stripe automatically calculates tax based on customer location

#### Webhook Handler (`POST /payments/stripe/webhook`)
- Captures tax breakdown from Stripe checkout session
- Extracts `total_details.breakdown.taxes` array from session
- Uses `VatCalculatorService.calculateVatInfoFromStripeBreakdown()` to parse VAT data
- Stores VAT object in proposal record

### 5. Invoice Generation Updates

**File:** `client/src/api/services/invoice-generator.service.ts`

- Updated `buildInvoiceData` method to use stored VAT percentage
- Replaced hardcoded 20% VAT with dynamic value from `proposal.vat.percent`
- Maintains fallback to 20% if no VAT information is stored

## How It Works

### 1. Payment Flow
1. Customer initiates payment for a proposal
2. Stripe Tax automatically calculates VAT based on billing address
3. Customer completes payment through Stripe Checkout
4. Webhook receives payment completion event with tax details
5. System calculates and stores VAT information in proposal

### 2. VAT Determination Logic
- **If Stripe calculates VAT:** Extract percentage directly from `tax_rate_details.percentage`
- **If no VAT in breakdown:** Use default exemption reason `"чл.113 ал.9 от ЗДДС"`
- **Multiple tax types:** Find the entry with `tax_type === 'vat'`

### 3. Invoice Generation
- Uses stored VAT information when creating invoices via inv.bg API
- Applies correct VAT percentage or exemption reason
- Maintains compliance with Bulgarian tax regulations

## Stripe Tax Breakdown Structure

The system now uses Stripe's detailed tax breakdown from the checkout session:

```json
{
  "total_details": {
    "breakdown": {
      "taxes": [
        {
          "amount": 2000,
          "rate": "txr_1NvWxZK9l7WyX4XABC",
          "tax_rate_details": {
            "display_name": "VAT",
            "percentage": 20,
            "tax_type": "vat"
          }
        }
      ]
    }
  }
}
```

The service extracts:
- `rate.percentage`: Direct VAT percentage from Stripe TaxRate
- `rate.tax_type`: Identifies VAT vs other tax types ('vat', 'sales_tax', etc.)
- `rate.display_name`: Human-readable tax name
- `taxability_reason`: Stripe's reason for tax treatment, mapped to Bulgarian exemptions

## Taxability Reason Mapping

| Stripe Taxability Reason | Bulgarian VAT Exemption | Use Case |
|--------------------------|-------------------------|----------|
| `reverse_charge` | `чл.86, ал.3 и чл.21 от ЗДДС` | Bulgarian companies with VAT registration |
| `zero_rated` | `чл.21 ал.2 от ЗДДС` | EU companies with valid VAT number |
| `customer_exempt`, `not_subject_to_tax` | `чл.21 от ЗДДС` | International clients (non-EU) |
| `not_collecting`, `not_supported` | `чл.82, ал. 2 от ЗДДС` | Domestic clients without VAT registration |
| `product_exempt`, `product_exempt_holiday` | `чл.113 ал.9 от ЗДДС` | General exemptions |
| Default/Unknown | `чл.113 ал.9 от ЗДДС` | Fallback case |

## Benefits

1. **Accurate Tax Data:** Uses Stripe's precise tax calculations and breakdown
2. **Direct VAT Extraction:** No manual calculation needed, uses Stripe's percentage directly
3. **Multi-Tax Support:** Handles multiple tax types and finds VAT specifically
4. **Automatic Compliance:** Stripe Tax handles complex international tax rules
5. **Proper Documentation:** Stores exemption reasons for audit purposes
6. **Bulgarian Compliance:** Includes proper ЗДДС exemption reasons

## Testing

A comprehensive test suite is included in:
`client/src/api/services/vat-calculator.service.spec.ts`

Tests cover:
- VAT extraction from Stripe tax breakdown
- Handling multiple tax types
- Exemption reason determination when no VAT found
- Percentage formatting and parsing
- Validation of exemption reasons

**Test Results:** ✅ All 14 tests passing

Tests include:
- Proper Stripe type validation and mock data
- VAT extraction from tax breakdown
- Taxability reason mapping to Bulgarian VAT exemptions
- Multiple tax type handling
- Edge cases and fallback scenarios

## Configuration Requirements

### Stripe Dashboard
1. Enable Stripe Tax in your Stripe dashboard
2. Configure tax settings for your business location
3. Set up automatic tax collection

### Environment Variables
Ensure the following are configured:
- `STRIPE_SECRET_KEY` - Your Stripe secret key
- `STRIPE_WEBHOOK_SECRET` - Webhook endpoint secret

## Migration

To apply the database changes:
```bash
supabase db reset
```

This will apply all migrations including the new VAT column.

## Future Enhancements

1. **Manual VAT Override:** Allow manual VAT adjustment for special cases
2. **Tax Reporting:** Generate tax reports based on stored VAT data
3. **Multi-Currency Support:** Enhanced tax calculation for different currencies
4. **Audit Trail:** Track VAT calculation changes and reasons
