# Stripe Tax Integration and VAT Object Implementation

## Overview

This implementation integrates Stripe Tax for automatic VAT calculation and replaces the hardcoded VAT percentage with a dynamic VAT object structure that stores both the percentage and exemption reasons.

## Changes Made

### 1. Database Schema Changes

**File:** `supabase/migrations/20250527195119_add_vat_to_proposals.sql`

- Added `vat` JSONB column to the `proposals` table
- Structure: `{percent: string, reason_without: string | null}`
- Added GIN index for efficient querying

### 2. TypeScript Models and Validators

**Files:**
- `client/src/lib/models/proposal.model.ts`
- `client/src/lib/validators/proposal.validator.ts`

- Added `VatInfo` interface with `percent` and `reason_without` fields
- Updated `Proposal` interface to include optional `vat` field
- Added Zod schema validation for VAT information

### 3. VAT Calculator Service

**File:** `client/src/api/services/vat-calculator.service.ts`

New service that:
- Calculates VAT information from Stripe Tax results
- Determines appropriate exemption reasons based on billing information
- Supports Bulgarian VAT exemption reasons:
  - `"чл.113 ал.9 от ЗДДС"` - Default/fallback
  - `"чл.86, ал.3 и чл.21 от ЗДДС"` - Bulgarian company with VAT number
  - `"чл.82, ал. 2 от ЗДДС"` - Bulgarian company without VAT number
  - `"чл.21 от ЗДДС"` - Non-Bulgarian company
  - `"чл.21 ал.2 от ЗДДС"` - Alternative exemption

### 4. Stripe Tax Integration

**File:** `client/src/api/routes/proposals.routes.ts`

#### Payment Creation Endpoint (`POST /:id/payments`)
- Enabled Stripe Tax with `automatic_tax: { enabled: true }`
- Enhanced proposal query to include client billing information
- Stripe automatically calculates tax based on customer location

#### Webhook Handler (`POST /payments/stripe/webhook`)
- Captures tax calculation results from Stripe checkout session
- Extracts `amount_tax` and `amount_subtotal` from session
- Uses `VatCalculatorService` to determine VAT information
- Stores VAT object in proposal record

### 5. Invoice Generation Updates

**File:** `client/src/api/services/invoice-generator.service.ts`

- Updated `buildInvoiceData` method to use stored VAT percentage
- Replaced hardcoded 20% VAT with dynamic value from `proposal.vat.percent`
- Maintains fallback to 20% if no VAT information is stored

## How It Works

### 1. Payment Flow
1. Customer initiates payment for a proposal
2. Stripe Tax automatically calculates VAT based on billing address
3. Customer completes payment through Stripe Checkout
4. Webhook receives payment completion event with tax details
5. System calculates and stores VAT information in proposal

### 2. VAT Determination Logic
- **If Stripe calculates tax:** Store the calculated percentage
- **If no tax calculated:** Determine exemption reason based on:
  - Client location (Bulgarian vs. international)
  - VAT registration status
  - Company type

### 3. Invoice Generation
- Uses stored VAT information when creating invoices via inv.bg API
- Applies correct VAT percentage or exemption reason
- Maintains compliance with Bulgarian tax regulations

## Benefits

1. **Automatic Tax Compliance:** Stripe Tax handles complex international tax rules
2. **Accurate VAT Calculation:** Real-time calculation based on customer location
3. **Proper Documentation:** Stores exemption reasons for audit purposes
4. **Flexible System:** Supports both taxable and exempt transactions
5. **Bulgarian Compliance:** Includes proper ЗДДС exemption reasons

## Testing

A comprehensive test suite is included in:
`client/src/api/services/__tests__/vat-calculator.service.test.ts`

Tests cover:
- VAT calculation with and without Stripe tax
- Exemption reason determination
- Percentage formatting and parsing
- Validation of exemption reasons

## Configuration Requirements

### Stripe Dashboard
1. Enable Stripe Tax in your Stripe dashboard
2. Configure tax settings for your business location
3. Set up automatic tax collection

### Environment Variables
Ensure the following are configured:
- `STRIPE_SECRET_KEY` - Your Stripe secret key
- `STRIPE_WEBHOOK_SECRET` - Webhook endpoint secret

## Migration

To apply the database changes:
```bash
supabase db reset
```

This will apply all migrations including the new VAT column.

## Future Enhancements

1. **Manual VAT Override:** Allow manual VAT adjustment for special cases
2. **Tax Reporting:** Generate tax reports based on stored VAT data
3. **Multi-Currency Support:** Enhanced tax calculation for different currencies
4. **Audit Trail:** Track VAT calculation changes and reasons
