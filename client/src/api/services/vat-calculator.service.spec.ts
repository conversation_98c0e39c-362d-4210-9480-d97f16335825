import { VatCalculatorService } from './vat-calculator.service';

describe('VatCalculatorService', () => {
  describe('calculateVatInfoFromStripeBreakdown', () => {
    it('should return 0% VAT with exemption reason when no tax breakdown provided', () => {
      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown([]);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.113 ал.9 от ЗДДС');
    });

    it('should extract VAT percentage from Stripe tax breakdown', () => {
      const taxBreakdown = [
        {
          amount: 2000,
          rate: "txr_1NvWxZK9l7WyX4XABC",
          tax_rate_details: {
            display_name: "VAT",
            percentage: 20,
            tax_type: "vat"
          }
        }
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });

    it('should handle multiple tax types and find VAT', () => {
      const taxBreakdown = [
        {
          amount: 500,
          rate: "txr_other",
          tax_rate_details: {
            display_name: "Sales Tax",
            percentage: 5,
            tax_type: "sales_tax"
          }
        },
        {
          amount: 2000,
          rate: "txr_vat",
          tax_rate_details: {
            display_name: "VAT",
            percentage: 20,
            tax_type: "vat"
          }
        }
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });

    it('should return exemption reason when no VAT found in breakdown', () => {
      const taxBreakdown = [
        {
          amount: 500,
          rate: "txr_other",
          tax_rate_details: {
            display_name: "Sales Tax",
            percentage: 5,
            tax_type: "sales_tax"
          }
        }
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.113 ал.9 от ЗДДС');
    });

    it('should handle different VAT percentages', () => {
      const taxBreakdown = [
        {
          amount: 1900,
          rate: "txr_reduced_vat",
          tax_rate_details: {
            display_name: "Reduced VAT",
            percentage: 19,
            tax_type: "vat"
          }
        }
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(19);
      expect(result.reason_without).toBeNull();
    });

    it('should find VAT by display name when tax_type is not available', () => {
      const taxBreakdown = [
        {
          amount: 2000,
          rate: "txr_vat",
          tax_rate_details: {
            display_name: "VAT",
            percentage: 20
          }
        }
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });
  });

  describe('isValidExemptionReason', () => {
    it('should return true for valid exemption reasons', () => {
      const validReasons = [
        "чл.113 ал.9 от ЗДДС",
        "чл.86, ал.3 и чл.21 от ЗДДС",
        "чл.82, ал. 2 от ЗДДС",
        "чл.21 от ЗДДС",
        "чл.21 ал.2 от ЗДДС"
      ];

      validReasons.forEach(reason => {
        expect(VatCalculatorService.isValidExemptionReason(reason)).toBe(true);
      });
    });

    it('should return false for invalid exemption reasons', () => {
      expect(VatCalculatorService.isValidExemptionReason('Invalid reason')).toBe(false);
    });
  });

  describe('formatVatPercent', () => {
    it('should format decimal to percentage number', () => {
      expect(VatCalculatorService.formatVatPercent(0.20)).toBe(20);
      expect(VatCalculatorService.formatVatPercent(0.19)).toBe(19);
      expect(VatCalculatorService.formatVatPercent(0)).toBe(0);
    });
  });

  describe('parseVatPercent', () => {
    it('should parse percentage number to decimal', () => {
      expect(VatCalculatorService.parseVatPercent(20)).toBe(0.20);
      expect(VatCalculatorService.parseVatPercent(19.5)).toBe(0.195);
      expect(VatCalculatorService.parseVatPercent(0)).toBe(0);
    });
  });
});
