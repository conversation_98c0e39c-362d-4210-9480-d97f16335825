import { VatCalculatorService } from '../vat-calculator.service';
import { BillingInformation } from '../../../lib/validators';

describe('VatCalculatorService', () => {
  describe('calculateVatInfo', () => {
    it('should return 0% VAT with exemption reason when no tax calculated by Stripe', () => {
      const billingInfo: BillingInformation = {
        id: '123',
        client_id: '456',
        company_name: 'Test Company',
        vat_number: null,
        eik: '*********',
        mol: '<PERSON>',
        address: 'Test Address',
        country: 'Bulgaria',
        city: 'Sofia',
        is_bulgarian: true,
        created_at: '2024-01-01T00:00:00Z',
      };

      const result = VatCalculatorService.calculateVatInfo(0, 10000, billingInfo);

      expect(result.percent).toBe('0');
      expect(result.reason_without).toBe('чл.82, ал. 2 от ЗДДС');
    });

    it('should calculate VAT percentage when tax is calculated by Stripe', () => {
      const result = VatCalculatorService.calculateVatInfo(2000, 10000, null);

      expect(result.percent).toBe('20.0');
      expect(result.reason_without).toBeNull();
    });

    it('should use correct exemption reason for Bulgarian company with VAT number', () => {
      const billingInfo: BillingInformation = {
        id: '123',
        client_id: '456',
        company_name: 'Test Company',
        vat_number: 'BG*********',
        eik: '*********',
        mol: 'John Doe',
        address: 'Test Address',
        country: 'Bulgaria',
        city: 'Sofia',
        is_bulgarian: true,
        created_at: '2024-01-01T00:00:00Z',
      };

      const result = VatCalculatorService.calculateVatInfo(0, 10000, billingInfo);

      expect(result.percent).toBe('0');
      expect(result.reason_without).toBe('чл.86, ал.3 и чл.21 от ЗДДС');
    });

    it('should use correct exemption reason for non-Bulgarian company', () => {
      const billingInfo: BillingInformation = {
        id: '123',
        client_id: '456',
        company_name: 'Test Company',
        vat_number: 'DE*********',
        eik: null,
        mol: null,
        address: 'Test Address',
        country: 'Germany',
        city: 'Berlin',
        is_bulgarian: false,
        created_at: '2024-01-01T00:00:00Z',
      };

      const result = VatCalculatorService.calculateVatInfo(0, 10000, billingInfo);

      expect(result.percent).toBe('0');
      expect(result.reason_without).toBe('чл.21 от ЗДДС');
    });
  });

  describe('isValidExemptionReason', () => {
    it('should return true for valid exemption reasons', () => {
      const validReasons = [
        "чл.113 ал.9 от ЗДДС",
        "чл.86, ал.3 и чл.21 от ЗДДС",
        "чл.82, ал. 2 от ЗДДС",
        "чл.21 от ЗДДС",
        "чл.21 ал.2 от ЗДДС"
      ];

      validReasons.forEach(reason => {
        expect(VatCalculatorService.isValidExemptionReason(reason)).toBe(true);
      });
    });

    it('should return false for invalid exemption reasons', () => {
      expect(VatCalculatorService.isValidExemptionReason('Invalid reason')).toBe(false);
    });
  });

  describe('formatVatPercent', () => {
    it('should format decimal to percentage string', () => {
      expect(VatCalculatorService.formatVatPercent(0.20)).toBe('20.0');
      expect(VatCalculatorService.formatVatPercent(0.19)).toBe('19.0');
      expect(VatCalculatorService.formatVatPercent(0)).toBe('0.0');
    });
  });

  describe('parseVatPercent', () => {
    it('should parse percentage string to decimal', () => {
      expect(VatCalculatorService.parseVatPercent('20')).toBe(0.20);
      expect(VatCalculatorService.parseVatPercent('19.5')).toBe(0.195);
      expect(VatCalculatorService.parseVatPercent('0')).toBe(0);
    });
  });
});
