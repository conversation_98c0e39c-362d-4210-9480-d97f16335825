import { VatInfo } from '../../lib/validators';

/**
 * Stripe tax breakdown item interface
 */
interface StripeTaxBreakdownItem {
  amount: number;
  rate: string;
  tax_rate_details: {
    display_name: string;
    percentage: number;
    tax_type: string;
  };
}

/**
 * Service to determine VAT application based on Stripe Tax breakdown results
 */
export class VatCalculatorService {

  /**
   * Bulgarian VAT exemption reasons according to ЗДДС (VAT Act)
   */
  private static readonly VAT_EXEMPTION_REASONS = [
    "чл.113 ал.9 от ЗДДС",
    "чл.86, ал.3 и чл.21 от ЗДДС",
    "чл.82, ал. 2 от ЗДДС",
    "чл.21 от ЗДДС",
    "чл.21 ал.2 от ЗДДС"
  ];

  /**
   * Determines VAT information based on Stripe Tax calculation and billing information
   * @param stripeTaxAmount - Tax amount calculated by Stripe (in cents)
   * @param totalAmount - Total amount before tax (in cents)
   * @param billingInfo - Client billing information
   * @returns VAT information object
   */
  static calculateVatInfo(
    stripeTaxAmount: number,
    totalAmount: number,
    billingInfo?: BillingInformation | null
  ): VatInfo {
    // If no tax calculated by Stripe, determine exemption reason
    if (stripeTaxAmount === 0) {
      const exemptionReason = this.determineExemptionReason(billingInfo);
      return {
        percent: "0",
        reason_without: exemptionReason
      };
    }

    // Calculate VAT percentage from Stripe tax calculation
    const vatPercent = ((stripeTaxAmount / totalAmount) * 100).toFixed(1);

    return {
      percent: vatPercent,
      reason_without: null
    };
  }

  /**
   * Determines the appropriate VAT exemption reason based on billing information
   * @param billingInfo - Client billing information
   * @returns Appropriate exemption reason or default
   */
  private static determineExemptionReason(billingInfo?: BillingInformation | null): string {
    if (!billingInfo) {
      return this.VAT_EXEMPTION_REASONS[0]; // Default to чл.113 ал.9 от ЗДДС
    }

    // If client is Bulgarian and has VAT number, use чл.86, ал.3 и чл.21 от ЗДДС
    if (billingInfo.is_bulgarian && billingInfo.vat_number) {
      return this.VAT_EXEMPTION_REASONS[1];
    }

    // If client is Bulgarian but no VAT number, use чл.82, ал. 2 от ЗДДС
    if (billingInfo.is_bulgarian && !billingInfo.vat_number) {
      return this.VAT_EXEMPTION_REASONS[2];
    }

    // If client is not Bulgarian (EU/International), use чл.21 от ЗДДС
    if (!billingInfo.is_bulgarian) {
      return this.VAT_EXEMPTION_REASONS[3];
    }

    // Default fallback
    return this.VAT_EXEMPTION_REASONS[0];
  }

  /**
   * Validates if a VAT exemption reason is valid
   * @param reason - The exemption reason to validate
   * @returns True if valid, false otherwise
   */
  static isValidExemptionReason(reason: string): boolean {
    return this.VAT_EXEMPTION_REASONS.includes(reason);
  }

  /**
   * Gets all available VAT exemption reasons
   * @returns Array of valid exemption reasons
   */
  static getAvailableExemptionReasons(): string[] {
    return [...this.VAT_EXEMPTION_REASONS];
  }

  /**
   * Converts VAT percentage from decimal to string format
   * @param decimalPercent - VAT percentage as decimal (e.g., 0.20 for 20%)
   * @returns VAT percentage as string (e.g., "20")
   */
  static formatVatPercent(decimalPercent: number): string {
    return (decimalPercent * 100).toFixed(1);
  }

  /**
   * Parses VAT percentage from string to decimal
   * @param percentString - VAT percentage as string (e.g., "20")
   * @returns VAT percentage as decimal (e.g., 0.20)
   */
  static parseVatPercent(percentString: string): number {
    return parseFloat(percentString) / 100;
  }
}
