import { VatInfo } from '../../lib/validators';
import Stripe from 'stripe';

/**
 * Service to determine VAT application based on Stripe Tax breakdown results
 */
export class VatCalculatorService {

  /**
   * Bulgarian VAT exemption reasons according to ЗДДС (VAT Act)
   */
  private static readonly VAT_EXEMPTION_REASONS = [
    "чл.113 ал.9 от ЗДДС",
    "чл.86, ал.3 и чл.21 от ЗДДС",
    "чл.82, ал. 2 от ЗДДС",
    "чл.21 от ЗДДС",
    "чл.21 ал.2 от ЗДДС"
  ];

  /**
   * Determines VAT information based on Stripe Tax breakdown
   * @param taxBreakdown - Array of tax breakdown items from Stripe
   * @returns VAT information object
   */
  static calculateVatInfoFromStripeBreakdown(
    taxBreakdown: StripeTaxBreakdownItem[]
  ): VatInfo {
    // If no tax breakdown or empty array, return 0% with default exemption reason
    if (!taxBreakdown || taxBreakdown.length === 0) {
      return {
        percent: 0,
        reason_without: this.VAT_EXEMPTION_REASONS[0] // Default to чл.113 ал.9 от ЗДДС
      };
    }

    // Find VAT entry in the tax breakdown
    const vatEntry = taxBreakdown.find(
      tax => tax.tax_rate_details.tax_type === 'vat'
    );

    // If no VAT found, return 0% with exemption reason
    if (!vatEntry) {
      return {
        percent: 0,
        reason_without: this.VAT_EXEMPTION_REASONS[0]
      };
    }

    // Return the VAT percentage from Stripe
    return {
      percent: vatEntry.tax_rate_details.percentage,
      reason_without: null
    };
  }

  /**
   * Validates if a VAT exemption reason is valid
   * @param reason - The exemption reason to validate
   * @returns True if valid, false otherwise
   */
  static isValidExemptionReason(reason: string): boolean {
    return this.VAT_EXEMPTION_REASONS.includes(reason);
  }

  /**
   * Gets all available VAT exemption reasons
   * @returns Array of valid exemption reasons
   */
  static getAvailableExemptionReasons(): string[] {
    return [...this.VAT_EXEMPTION_REASONS];
  }

  /**
   * Converts VAT percentage from decimal to percentage number
   * @param decimalPercent - VAT percentage as decimal (e.g., 0.20 for 20%)
   * @returns VAT percentage as number (e.g., 20)
   */
  static formatVatPercent(decimalPercent: number): number {
    return decimalPercent * 100;
  }

  /**
   * Parses VAT percentage from number to decimal
   * @param percentNumber - VAT percentage as number (e.g., 20)
   * @returns VAT percentage as decimal (e.g., 0.20)
   */
  static parseVatPercent(percentNumber: number): number {
    return percentNumber / 100;
  }
}
